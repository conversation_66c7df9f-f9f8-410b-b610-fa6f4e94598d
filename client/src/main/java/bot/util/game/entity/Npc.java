package bot.util.game.entity;

import rt4.*;
import bot.api.interaction.NPCInteraction;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.game.world.Tile;
import bot.util.ui.ScreenCoordinateUtils;

/**
 * Represents an NPC in the game world and provides utility methods for working with NPCs.
 */
public class Npc {
    private static final Logger logger = LoggerFactory.getLogger(Npc.class);

    private final rt4.Npc underlying;
    private final int index;

    /**
     * Creates a new NPC wrapper.
     *
     * @param npc The underlying rt4.Npc object
     * @param index The index of the NPC in the NpcList
     */
    public Npc(rt4.Npc npc, int index) {
        this.underlying = npc;
        this.index = index;
    }

    /**
     * Gets the index of this NPC in the NpcList.
     *
     * @return The NPC index
     */
    public int getIndex() {
        return index;
    }

    /**
     * Gets the underlying rt4.Npc object.
     *
     * @return The underlying rt4.Npc object
     */
    public rt4.Npc getUnderlying() {
        return this.underlying;
    }

    /**
     * Gets the name of this NPC.
     *
     * @return The NPC's name, or "null" if not available
     */
    public String getName() {
        return this.underlying.type != null && this.underlying.type.name != null ? this.underlying.type.name.toString() : "null";
    }

    /**
     * Gets the overhead text of this NPC.
     *
     * @return The NPC's overhead text, or an empty string if not available
     */
    public String getOverheadText() {
        return this.underlying.chatMessage != null ? this.underlying.chatMessage.toString() : "";
    }

    /**
     * Gets the entity this NPC is facing.
     *
     * @return The entity index this NPC is facing
     */
    public int getFaceEntity() {
        return this.underlying.faceEntity;
    }

    /**
     * Gets the ID of this NPC.
     *
     * @return The NPC's ID, or -1 if not available
     */
    public int getId() {
        return this.underlying.type != null ? this.underlying.type.id : -1;
    }

    /**
     * Gets the X coordinate of this NPC.
     *
     * @return The NPC's X coordinate
     */
    public int getX() {
        return (underlying.xFine >> 7) + rt4.Camera.originX;
    }

    /**
     * Gets the Y coordinate of this NPC.
     *
     * @return The NPC's Y coordinate
     */
    public int getY() {
        return (underlying.zFine >> 7) + rt4.Camera.originZ;
    }

    /**
     * Gets the location of this NPC.
     *
     * @return The NPC's location as a Tile
     */
    public Tile getLocation() {
        return new Tile(getX(), getY());
    }

    /**
     * Gets the local X coordinate of this NPC.
     *
     * @return The NPC's local X coordinate
     */
    public int getLocalX() {
        return underlying.xFine >> 7;
    }

    /**
     * Gets the local Y coordinate of this NPC.
     *
     * @return The NPC's local Y coordinate
     */
    public int getLocalY() {
        return underlying.zFine >> 7;
    }

    /**
     * Gets the animation ID of this NPC.
     *
     * @return The NPC's animation ID
     */
    public int getAnimation() {
        return underlying.seqId;
    }

    /**
     * Gets the available actions for this NPC.
     *
     * @return The NPC's actions as JagString array
     */
    public JagString[] getActions() {
        return underlying.type.ops;
    }

    /**
     * Calculates the distance from this NPC to another tile.
     *
     * @param other The tile to calculate distance to
     * @return The distance in tiles
     */
    public double distanceTo(Tile other) {
        return getLocation().distanceTo(other);
    }

    /**
     * Checks if this NPC is equal to another NPC.
     *
     * @param other The other NPC to compare with
     * @return true if the NPCs are equal, false otherwise
     */
    public boolean equals(Npc other) {
        if (other == null) {
            return false;
        }
        if (!(other instanceof Npc)) {
            return false;
        }
        Npc otherNpc = (Npc) other;
        return getId() == otherNpc.getId() && otherNpc.getIndex() == getIndex();
    }

    /**
     * Checks if this NPC exists in the game world.
     *
     * @return true if the NPC exists, false otherwise
     */
    public boolean exists() {
        return underlying != null && underlying.type != null;
    }

    /**
     * Checks if this NPC is on screen.
     *
     * @return true if the NPC is on screen, false otherwise
     */
    public boolean isOnScreen() {
        if (!exists()) {
            return false;
        }

        // Get the NPC's location
        Tile location = getLocation();
        if (location == null) {
            return false;
        }

        // Check if the NPC is within the camera's view
        return bot.util.entity.ScreenUtils.isOnScreen(location.getX(), location.getY());
    }

    /**
     * Interacts with this NPC using the specified option.
     *
     * @param option The option to select
     * @return true if the interaction was successful, false otherwise
     */
    public boolean interact(String option) {
        if (!exists()) {
            return false;
        }

        try {
            // Use NPCInteraction to interact with the NPC
            return NPCInteraction.interactWithNPC(this, option);
        } catch (Exception e) {
            return false;
        }
    }

    // Static utility methods (moved from Npcs class)

    /**
     * Gets all NPCs in the game.
     *
     * @return A list of all NPCs
     */
    public static List<Npc> getAll() {
        List<Npc> npcs = new ArrayList<>();

        try {
            // Iterate through all NPCs in the NpcList
            for (int i = 0; i < rt4.NpcList.size; i++) {
                int npcIndex = rt4.NpcList.ids[i];
                rt4.Npc rtNpc = rt4.NpcList.npcs[npcIndex];

                if (rtNpc != null) {
                    npcs.add(new Npc(rtNpc, npcIndex));
                }
            }
        } catch (Exception e) {
            logger.error("Error getting all NPCs", e);
        }

        return npcs;
    }

    /**
     * Gets all NPCs that match the given predicate.
     *
     * @param predicate The predicate to match NPCs against
     * @return A list of matching NPCs
     */
    public static List<Npc> getAll(Predicate<Npc> predicate) {
        List<Npc> npcs = new ArrayList<>();

        try {
            // Iterate through all NPCs in the NpcList
            for (int i = 0; i < rt4.NpcList.size; i++) {
                int npcIndex = rt4.NpcList.ids[i];
                rt4.Npc rtNpc = rt4.NpcList.npcs[npcIndex];

                if (rtNpc != null) {
                    Npc npc = new Npc(rtNpc, npcIndex);
                    if (predicate.test(npc)) {
                        npcs.add(npc);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error getting NPCs with predicate", e);
        }

        return npcs;
    }

    /**
     * Gets all NPCs with the given ID.
     *
     * @param id The ID of the NPCs to get
     * @return A list of matching NPCs
     */
    public static List<Npc> getAll(int id) {
        return getAll(npc -> npc.getId() == id);
    }

    /**
     * Gets all NPCs with the given name.
     *
     * @param name The name of the NPCs to get
     * @return A list of matching NPCs
     */
    public static List<Npc> getAll(String name) {
        return getAll(npc ->
            npc.getName() != null &&
            npc.getName().equals(name)
        );
    }

    /**
     * Gets the nearest NPC that matches the given predicate.
     *
     * @param predicate The predicate to match NPCs against
     * @return The nearest matching NPC, or null if none found
     */
    public static Npc getNearest(Predicate<Npc> predicate) {
        Tile playerLocation = Player.getLocation();
        if (playerLocation == null) {
            return null;
        }

        Npc nearest = null;
        int nearestDistance = Integer.MAX_VALUE;

        for (Npc npc : getAll(predicate)) {
            Tile npcLocation = npc.getLocation();
            if (npcLocation != null) {
                int distance = (int) playerLocation.distanceTo(npcLocation);
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearest = npc;
                }
            }
        }

        return nearest;
    }

    /**
     * Gets the nearest NPC with the given ID.
     *
     * @param id The ID of the NPC to get
     * @return The nearest matching NPC, or null if none found
     */
    public static Npc getNearest(int id) {
        return getNearest(npc -> npc.getId() == id);
    }

    /**
     * Gets the nearest NPC with the given name.
     *
     * @param name The name of the NPC to get
     * @return The nearest matching NPC, or null if none found
     */
    public static Npc getNearest(String name) {
        return getNearest(npc ->
            npc.getName() != null &&
            npc.getName().equals(name)
        );
    }
}
