package bot.util.ui;

import bot.util.entity.ScreenUtils;
import rt4.GameShell;

/**
 * Utility class for screen coordinate operations and bounds checking.
 * This consolidates simple screen coordinate utilities while preserving
 * the complex 3D projection math in ScreenUtils.
 * 
 * Note: Complex 3D-to-2D projection logic remains in ScreenUtils.worldToScreen()
 * as it contains critical game engine math that should not be modified.
 */
public class ScreenCoordinateUtils {
    
    /**
     * Fixed mode screen dimensions (RuneScape 2009 client)
     */
    public static final int FIXED_WIDTH = 512;
    public static final int FIXED_HEIGHT = 334;
    
    /**
     * Fixed mode center coordinates
     */
    public static final int FIXED_CENTER_X = 256;
    public static final int FIXED_CENTER_Y = 167;
    
    /**
     * Private constructor to prevent instantiation
     */
    private ScreenCoordinateUtils() {
        // Utility class
    }
    
    // ===== SCREEN BOUNDS CHECKING =====
    
    /**
     * Checks if a point is within the screen bounds.
     * This consolidates the logic from ScreenUtils.isOnScreen() and other classes.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return true if the point is on the screen
     */
    public static boolean isOnScreen(int x, int y) {
        return x >= 0 && x < GameShell.canvasWidth &&
               y >= 0 && y < GameShell.canvasHeight;
    }
    
    /**
     * Checks if a point is within fixed mode bounds.
     * Fixed mode is the standard 512x334 game view.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return true if the point is within fixed mode bounds
     */
    public static boolean isInFixedBounds(int x, int y) {
        return x >= 0 && x < FIXED_WIDTH && y >= 0 && y < FIXED_HEIGHT;
    }
    
    /**
     * Checks if screen coordinates are valid and on screen.
     * This is a convenience method for ScreenCoords objects.
     * 
     * @param coords The screen coordinates to check
     * @return true if coordinates are valid and on screen
     */
    public static boolean isValidAndOnScreen(ScreenUtils.ScreenCoords coords) {
        return coords != null && coords.isOnScreen && isOnScreen(coords.x, coords.y);
    }
    
    // ===== COORDINATE CLAMPING =====
    
    /**
     * Clamps coordinates to screen bounds.
     * Ensures coordinates stay within the visible screen area.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return Clamped coordinates as [x, y]
     */
    public static int[] clampToScreen(int x, int y) {
        int clampedX = Math.max(0, Math.min(GameShell.canvasWidth - 1, x));
        int clampedY = Math.max(0, Math.min(GameShell.canvasHeight - 1, y));
        return new int[]{clampedX, clampedY};
    }
    
    /**
     * Clamps coordinates to fixed mode bounds.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return Clamped coordinates as [x, y]
     */
    public static int[] clampToFixedBounds(int x, int y) {
        int clampedX = Math.max(0, Math.min(FIXED_WIDTH - 1, x));
        int clampedY = Math.max(0, Math.min(FIXED_HEIGHT - 1, y));
        return new int[]{clampedX, clampedY};
    }
    
    // ===== COORDINATE VALIDATION =====
    
    /**
     * Validates that coordinates are reasonable for mouse interaction.
     * Checks for negative values, extreme coordinates, or invalid ranges.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return true if coordinates are valid for interaction
     */
    public static boolean areValidInteractionCoords(int x, int y) {
        // Check for obviously invalid coordinates
        if (x < -100 || y < -100 || x > 2000 || y > 2000) {
            return false;
        }
        
        // Check if coordinates are at least close to the screen
        return x >= -50 && x <= GameShell.canvasWidth + 50 &&
               y >= -50 && y <= GameShell.canvasHeight + 50;
    }
    
    /**
     * Creates a safe ScreenCoords object with bounds checking.
     * This ensures the coordinates are valid and properly marked as on/off screen.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return A ScreenCoords object with proper bounds checking
     */
    public static ScreenUtils.ScreenCoords createSafeScreenCoords(int x, int y) {
        boolean onScreen = isOnScreen(x, y);
        return new ScreenUtils.ScreenCoords(x, y, onScreen);
    }
    
    // ===== DISTANCE AND POSITIONING =====
    
    /**
     * Calculates the distance between two screen points.
     * 
     * @param x1 First point x-coordinate
     * @param y1 First point y-coordinate
     * @param x2 Second point x-coordinate
     * @param y2 Second point y-coordinate
     * @return The distance in pixels
     */
    public static double calculateScreenDistance(int x1, int y1, int x2, int y2) {
        int dx = x2 - x1;
        int dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Calculates the distance from a point to the screen center.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return The distance from screen center in pixels
     */
    public static double distanceFromCenter(int x, int y) {
        int centerX = GameShell.canvasWidth / 2;
        int centerY = GameShell.canvasHeight / 2;
        return calculateScreenDistance(x, y, centerX, centerY);
    }
    
    /**
     * Calculates the distance from a point to the fixed mode center.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return The distance from fixed mode center in pixels
     */
    public static double distanceFromFixedCenter(int x, int y) {
        return calculateScreenDistance(x, y, FIXED_CENTER_X, FIXED_CENTER_Y);
    }
    
    // ===== COORDINATE CONVERSION HELPERS =====
    
    /**
     * Converts screen coordinates to a percentage of screen dimensions.
     * Useful for relative positioning and scaling.
     * 
     * @param x The x-coordinate
     * @param y The y-coordinate
     * @return Percentage coordinates as [xPercent, yPercent] (0.0 to 1.0)
     */
    public static double[] toScreenPercentage(int x, int y) {
        double xPercent = (double) x / GameShell.canvasWidth;
        double yPercent = (double) y / GameShell.canvasHeight;
        return new double[]{xPercent, yPercent};
    }
    
    /**
     * Converts percentage coordinates to screen coordinates.
     * 
     * @param xPercent The x percentage (0.0 to 1.0)
     * @param yPercent The y percentage (0.0 to 1.0)
     * @return Screen coordinates as [x, y]
     */
    public static int[] fromScreenPercentage(double xPercent, double yPercent) {
        int x = (int) (xPercent * GameShell.canvasWidth);
        int y = (int) (yPercent * GameShell.canvasHeight);
        return new int[]{x, y};
    }
}
