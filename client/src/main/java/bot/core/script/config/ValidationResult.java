package bot.core.script.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Immutable result of a validation operation.
 * Contains success/failure status and detailed error messages.
 * 
 * <AUTHOR> Agent
 */
public class ValidationResult {
    private final boolean valid;
    private final List<String> errors;
    
    /**
     * Private constructor for creating validation results.
     */
    private ValidationResult(boolean valid, List<String> errors) {
        this.valid = valid;
        this.errors = errors != null ? Collections.unmodifiableList(new ArrayList<>(errors)) : Collections.emptyList();
    }
    
    /**
     * Creates a successful validation result.
     * 
     * @return A successful validation result
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null);
    }
    
    /**
     * Creates a failed validation result with a single error message.
     * 
     * @param errorMessage The error message
     * @return A failed validation result
     */
    public static ValidationResult failure(String errorMessage) {
        List<String> errors = new ArrayList<>();
        errors.add(errorMessage);
        return new ValidationResult(false, errors);
    }
    
    /**
     * Creates a failed validation result with multiple error messages.
     * 
     * @param errorMessages The error messages
     * @return A failed validation result
     */
    public static ValidationResult failure(List<String> errorMessages) {
        return new ValidationResult(false, errorMessages);
    }
    
    /**
     * Combines multiple validation results into one.
     * The result is valid only if all input results are valid.
     * 
     * @param results The validation results to combine
     * @return A combined validation result
     */
    public static ValidationResult combine(ValidationResult... results) {
        List<String> allErrors = new ArrayList<>();
        boolean allValid = true;
        
        for (ValidationResult result : results) {
            if (!result.isValid()) {
                allValid = false;
                allErrors.addAll(result.getErrors());
            }
        }
        
        return allValid ? success() : new ValidationResult(false, allErrors);
    }
    
    /**
     * Checks if the validation was successful.
     * 
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return valid;
    }
    
    /**
     * Checks if the validation failed.
     * 
     * @return true if invalid, false otherwise
     */
    public boolean isInvalid() {
        return !valid;
    }
    
    /**
     * Gets all error messages from the validation.
     * 
     * @return Immutable list of error messages (empty if valid)
     */
    public List<String> getErrors() {
        return errors;
    }
    
    /**
     * Gets the first error message, if any.
     * 
     * @return The first error message, or null if valid
     */
    public String getFirstError() {
        return errors.isEmpty() ? null : errors.get(0);
    }
    
    /**
     * Gets the number of validation errors.
     * 
     * @return The error count
     */
    public int getErrorCount() {
        return errors.size();
    }
    
    /**
     * Checks if there are any validation errors.
     * 
     * @return true if there are errors, false otherwise
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * Gets all error messages as a single formatted string.
     * 
     * @return Formatted error string, or empty string if valid
     */
    public String getFormattedErrors() {
        if (errors.isEmpty()) {
            return "";
        }
        
        if (errors.size() == 1) {
            return errors.get(0);
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < errors.size(); i++) {
            if (i > 0) {
                sb.append("; ");
            }
            sb.append(errors.get(i));
        }
        return sb.toString();
    }
    
    @Override
    public String toString() {
        if (valid) {
            return "ValidationResult{valid=true}";
        } else {
            return "ValidationResult{valid=false, errors=" + errors + "}";
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ValidationResult that = (ValidationResult) o;
        return valid == that.valid && Objects.equals(errors, that.errors);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(valid, errors);
    }
}
