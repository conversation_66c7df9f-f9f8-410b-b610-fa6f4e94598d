package bot.api.interaction;

import bot.util.game.entity.Item;
import bot.util.game.entity.Npc;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import rt4.*;

/**
 * Provides methods for interacting with game interfaces and widgets.
 * This class handles all widget-related interactions including clicking options,
 * handling dialogues, and interacting with specific interface components.
 */
public class WidgetInteraction {
    private static final Logger logger = LoggerFactory.getLogger(WidgetInteraction.class);

    // Common interface constants
    public static final int CHATBOX_INTERFACE = 752 << 16 | 12;
    public static final int CENTER_RESIZABLE = 746 << 16 | 6;
    public static final int CENTER_FIXED = 548 << 16 | 11;

    // Common interface component IDs
    public static class Components {
        // Skilling interfaces
        public static final int FLETCHING_UNSTRUNG = 304;
        public static final int COOKING = 307;
        public static final int NORMAL_FLETCHING = 305;
        public static final int FLETCHING_STRUNG = 309;
        public static final int SPINNING_WHEEL = 459;

        // Fletching interface components
        public static final int FLETCHING_SHORTBOW = NORMAL_FLETCHING << 16 | 6;
        public static final int FLETCHING_LONGBOW = NORMAL_FLETCHING << 16 | 7;
        public static final int FLETCHING_ARROW_SHAFT = NORMAL_FLETCHING << 16 | 8;
        public static final int FLETCHING_CROSSBOW_STOCK = NORMAL_FLETCHING << 16 | 9;

        // Strung bow fletching components
        public static final int FLETCHING_SHORTBOW_U = FLETCHING_UNSTRUNG << 16 | 6;
        public static final int FLETCHING_LONGBOW_U = FLETCHING_UNSTRUNG << 16 | 7;

        // Strung bow fletching components
        public static final int FLETCHING_SHORTBOW_S = FLETCHING_STRUNG << 16 | 6;
        public static final int FLETCHING_LONGBOW_S = FLETCHING_STRUNG << 16 | 7;
        public static final int BANK = 762;
        public static final int BANK_INVENTORY = 763;
        public static final int BANK_WITHDRAW = BANK << 16 | 73;
        public static final int BANK_DEPOSIT = BANK_INVENTORY << 16;
        public static final int SMELTING = 311;
        public static final int SMELTING_BRONZE = SMELTING << 16 | 13;
        public static final int SMELTING_IRON = SMELTING << 16 | 14;
        public static final int SMELTING_SILVER = SMELTING << 16 | 15;
        public static final int SMELTING_STEEL = SMELTING << 16 | 16;
        public static final int SMELTING_GOLD = SMELTING << 16 | 17;
        public static final int SMELTING_MITHRIL = SMELTING << 16 | 18;
        public static final int SMELTING_ADAMANT = SMELTING << 16 | 19;
        public static final int SMELTING_RUNITE = SMELTING << 16 | 20;

        // Magic interface IDs
        public static final int MAGIC = 192;
        public static final int MAGIC_MODERN = 192;
        public static final int MAGIC_ANCIENT = 193;
        public static final int MAGIC_LUNAR = 430;

        // Magic spell IDs (standard spellbook)
        public static final int SPELL_HOME_TELEPORT = MAGIC << 16;
        public static final int SPELL_WIND_STRIKE = MAGIC << 16 | 1;
        public static final int SPELL_CONFUSE = MAGIC << 16 | 2;
        public static final int SPELL_WATER_STRIKE = MAGIC << 16 | 4;
        public static final int SPELL_LVL1_ENCHANT = MAGIC << 16 | 5;
        public static final int SPELL_EARTH_STRIKE = MAGIC << 16 | 6;
        public static final int SPELL_WEAKEN = MAGIC << 16 | 7;
        public static final int SPELL_FIRE_STRIKE = MAGIC << 16 | 8;
        public static final int SPELL_BONES_TO_BANANAS = MAGIC << 16 | 9;
        public static final int SPELL_WIND_BOLT = MAGIC << 16 | 10;
        public static final int SPELL_CURSE = MAGIC << 16 | 11;
        public static final int SPELL_BIND = MAGIC << 16 | 12;
        public static final int SPELL_LOW_LEVEL_ALCHEMY = MAGIC << 16 | 13;
        public static final int SPELL_WATER_BOLT = MAGIC << 16 | 14;
        public static final int SPELL_VARROCK_TELEPORT = MAGIC << 16 | 15;
        public static final int SPELL_LVL2_ENCHANT = MAGIC << 16 | 16;
        public static final int SPELL_EARTH_BOLT = MAGIC << 16 | 17;
        public static final int SPELL_TELEKINETIC_GRAB = MAGIC << 16 | 19;
        public static final int SPELL_FIRE_BOLT = MAGIC << 16 | 20;
        public static final int SPELL_FALADOR_TELEPORT = MAGIC << 16 | 21;
        public static final int SPELL_CRUMBLE_UNDEAD = MAGIC << 16 | 22;
        public static final int SPELL_HOUSE_TELEPORT = MAGIC << 16 | 23;
        public static final int SPELL_WIND_BLAST = MAGIC << 16 | 24;
        public static final int SPELL_SUPERHEAT_ITEM = MAGIC << 16 | 25;
        public static final int SPELL_CAMELOT_TELEPORT = MAGIC << 16 | 26;
        public static final int SPELL_WATER_BLAST = MAGIC << 16 | 27;
        public static final int SPELL_LVL3_ENCHANT = MAGIC << 16 | 28;
        public static final int SPELL_IBAN_BLAST = MAGIC << 16 | 29;
        public static final int SPELL_SNARE = MAGIC << 16 | 30;
        public static final int SPELL_MAGIC_DART = MAGIC << 16 | 31;
        public static final int SPELL_ARDOUGNE_TELEPORT = MAGIC << 16 | 32;
        public static final int SPELL_EARTH_BLAST = MAGIC << 16 | 33;
        public static final int SPELL_HIGH_LEVEL_ALCHEMY = MAGIC << 16 | 34;
        public static final int SPELL_CHARGE_WATER_ORB = MAGIC << 16 | 35;
        public static final int SPELL_LVL4_ENCHANT = MAGIC << 16 | 36;
        public static final int SPELL_WATCHTOWER_TELEPORT = MAGIC << 16 | 37;
        public static final int SPELL_FIRE_BLAST = MAGIC << 16 | 38;
        public static final int SPELL_CHARGE_EARTH_ORB = MAGIC << 16 | 39;
        public static final int SPELL_BONES_TO_PEACHES = MAGIC << 16 | 40;
        public static final int SPELL_SARADOMIN_STRIKE = MAGIC << 16 | 41;
        public static final int SPELL_CLAWS_OF_GUTHIX = MAGIC << 16 | 42;
        public static final int SPELL_FLAMES_OF_ZAMOROK = MAGIC << 16 | 43;
        public static final int SPELL_TROLLHEIM_TELEPORT = MAGIC << 16 | 44;
        public static final int SPELL_WIND_WAVE = MAGIC << 16 | 45;
        public static final int SPELL_CHARGE_FIRE_ORB = MAGIC << 16 | 46;
        public static final int SPELL_APE_ATOLL_TELEPORT = MAGIC << 16 | 47;
        public static final int SPELL_WATER_WAVE = MAGIC << 16 | 48;
        public static final int SPELL_CHARGE_AIR_ORB = MAGIC << 16 | 49;
        public static final int SPELL_VULNERABILITY = MAGIC << 16 | 50;
        public static final int SPELL_LVL5_ENCHANT = MAGIC << 16 | 51;
        public static final int SPELL_EARTH_WAVE = MAGIC << 16 | 52;
        public static final int SPELL_ENFEEBLE = MAGIC << 16 | 53;
        public static final int SPELL_TELE_OTHER_LUMBRIDGE = MAGIC << 16 | 54;
        public static final int SPELL_FIRE_WAVE = MAGIC << 16 | 55;
        public static final int SPELL_ENTANGLE = MAGIC << 16 | 56;
        public static final int SPELL_STUN = MAGIC << 16 | 57;
        public static final int SPELL_CHARGE = MAGIC << 16 | 58;
        public static final int SPELL_TELE_OTHER_FALADOR = MAGIC << 16 | 59;
        public static final int SPELL_TELEBLOCK = MAGIC << 16 | 60;
        public static final int SPELL_LVL6_ENCHANT = MAGIC << 16 | 61;
        public static final int SPELL_TELE_OTHER_CAMELOT = MAGIC << 16 | 62;

        // Prayer interface IDs
        public static final int PRAYER = 541;

        // Prayer component IDs
        public static final int PRAYER_THICK_SKIN = PRAYER << 16 | 4;
        public static final int PRAYER_BURST_OF_STRENGTH = PRAYER << 16 | 5;
        public static final int PRAYER_CLARITY_OF_THOUGHT = PRAYER << 16 | 6;
        public static final int PRAYER_ROCK_SKIN = PRAYER << 16 | 7;
        public static final int PRAYER_SUPERHUMAN_STRENGTH = PRAYER << 16 | 8;
        public static final int PRAYER_IMPROVED_REFLEXES = PRAYER << 16 | 9;
        public static final int PRAYER_RAPID_RESTORE = PRAYER << 16 | 10;
        public static final int PRAYER_RAPID_HEAL = PRAYER << 16 | 11;
        public static final int PRAYER_PROTECT_ITEM = PRAYER << 16 | 12;
        public static final int PRAYER_STEEL_SKIN = PRAYER << 16 | 13;
        public static final int PRAYER_ULTIMATE_STRENGTH = PRAYER << 16 | 14;
        public static final int PRAYER_INCREDIBLE_REFLEXES = PRAYER << 16 | 15;
        public static final int PRAYER_PROTECT_FROM_MAGIC = PRAYER << 16 | 16;
        public static final int PRAYER_PROTECT_FROM_MISSILES = PRAYER << 16 | 17;
        public static final int PRAYER_PROTECT_FROM_MELEE = PRAYER << 16 | 18;
        public static final int PRAYER_RETRIBUTION = PRAYER << 16 | 19;
        public static final int PRAYER_REDEMPTION = PRAYER << 16 | 20;
        public static final int PRAYER_SMITE = PRAYER << 16 | 21;
        public static final int PRAYER_CHIVALRY = PRAYER << 16 | 22;
        public static final int PRAYER_PIETY = PRAYER << 16 | 23;

        // Health and run energy interface IDs
        public static final int HEALTH_INTERFACE = 748;
        public static final int HEALTH_COMPONENT = HEALTH_INTERFACE << 16 | 8;
        public static final int RUN_BUTTON_INTERFACE = 750;
        public static final int RUN_BUTTON_COMPONENT = RUN_BUTTON_INTERFACE << 16 | 6;

        // Combat interface IDs
        public static final int SPECIAL_ATTACK_INTERFACE = 884;
        public static final int SPECIAL_ATTACK_BUTTON = SPECIAL_ATTACK_INTERFACE << 16 | 4;
        public static final int SPECIAL_ATTACK_ENERGY = SPECIAL_ATTACK_INTERFACE << 16 | 8;

        // Equipment interface IDs
        public static final int EQUIPMENT_INTERFACE = 387;
        public static final int EQUIPMENT_COMPONENT = EQUIPMENT_INTERFACE << 16 | 1;

        // Inventory interface IDs
        public static final int INVENTORY_INTERFACE = 149;
        public static final int INVENTORY_COMPONENT = INVENTORY_INTERFACE << 16 | 0;

        // Smithing interface IDs
        public static final int SMITHING = 312;
        public static final int SMITHING_DAGGER = SMITHING << 16 | 2;
        public static final int SMITHING_SWORD = SMITHING << 16 | 3;
        public static final int SMITHING_SCIMITAR = SMITHING << 16 | 4;
        public static final int SMITHING_LONGSWORD = SMITHING << 16 | 5;
        public static final int SMITHING_2H_SWORD = SMITHING << 16 | 6;
        public static final int SMITHING_AXE = SMITHING << 16 | 7;
        public static final int SMITHING_MACE = SMITHING << 16 | 8;
        public static final int SMITHING_WARHAMMER = SMITHING << 16 | 9;
        public static final int SMITHING_BATTLEAXE = SMITHING << 16 | 10;
        public static final int SMITHING_CHAINBODY = SMITHING << 16 | 11;
        public static final int SMITHING_PLATELEGS = SMITHING << 16 | 12;
        public static final int SMITHING_PLATESKIRT = SMITHING << 16 | 13;
        public static final int SMITHING_PLATEBODY = SMITHING << 16 | 14;
        public static final int SMITHING_NAILS = SMITHING << 16 | 15;
        public static final int SMITHING_MEDIUM_HELM = SMITHING << 16 | 16;
        public static final int SMITHING_FULL_HELM = SMITHING << 16 | 17;
        public static final int SMITHING_SQUARE_SHIELD = SMITHING << 16 | 18;
        public static final int SMITHING_KITESHIELD = SMITHING << 16 | 19;
        public static final int SMITHING_DART_TIPS = SMITHING << 16 | 20;
        public static final int SMITHING_ARROWTIPS = SMITHING << 16 | 21;
        public static final int SMITHING_KNIVES = SMITHING << 16 | 22;
        public static final int SMITHING_LIMBS = SMITHING << 16 | 23;

        // Tab interface IDs and components
        public static final int TAB_INTERFACE = 548;

        // Tab indices
        public static final int TAB_ATTACK = 0;
        public static final int TAB_TASK = 1;
        public static final int TAB_STATS = 2;
        public static final int TAB_QUESTS = 3;
        public static final int TAB_INVENTORY = 4;
        public static final int TAB_EQUIPMENT = 5;
        public static final int TAB_PRAYER = 6;
        public static final int TAB_MAGIC = 7;
        public static final int TAB_SUMMONING = 8;
        public static final int TAB_FRIENDS = 9;
        public static final int TAB_FRIENDS_CHAT = 10;
        public static final int TAB_CLAN = 11;
        public static final int TAB_OPTIONS = 12;
        public static final int TAB_CONTROLS = 13;
        public static final int TAB_MUSIC = 14;
        public static final int TAB_NOTES = 15;
        public static final int TAB_LOGOUT = 16;



        // Dialog interface IDs
        public static final int DIALOG_CONTINUE_START = 210;
        public static final int DIALOG_CONTINUE_END = 220;
        public static final int DIALOG_OPTIONS_START = 228;
        public static final int DIALOG_OPTIONS_END = 235;

        // Grand Exchange interface IDs
        public static final int GRAND_EXCHANGE_INTERFACE = 105;
        public static final int GRAND_EXCHANGE_OFFER_1 = GRAND_EXCHANGE_INTERFACE << 16 | 7;
        public static final int GRAND_EXCHANGE_OFFER_2 = GRAND_EXCHANGE_INTERFACE << 16 | 8;
        public static final int GRAND_EXCHANGE_OFFER_3 = GRAND_EXCHANGE_INTERFACE << 16 | 9;
        public static final int GRAND_EXCHANGE_OFFER_4 = GRAND_EXCHANGE_INTERFACE << 16 | 10;
        public static final int GRAND_EXCHANGE_OFFER_5 = GRAND_EXCHANGE_INTERFACE << 16 | 11;
        public static final int GRAND_EXCHANGE_OFFER_6 = GRAND_EXCHANGE_INTERFACE << 16 | 12;
        public static final int GRAND_EXCHANGE_COLLECT = GRAND_EXCHANGE_INTERFACE << 16 | 13;
        public static final int GRAND_EXCHANGE_HISTORY = GRAND_EXCHANGE_INTERFACE << 16 | 14;
    }

    // Dialog-related constants and methods have been moved to DialogInteraction

    /**
     * Checks if a specific interface is currently open
     *
     * @param interfaceId The interface ID to check
     * @return true if the interface is open, false otherwise
     */
    public static boolean isInterfaceOpen(int interfaceId) {
        // Check if the interface is in the openInterfaces hash table
        for (Object obj = InterfaceList.openInterfaces.head(); obj != null; obj = InterfaceList.openInterfaces.next()) {
            if (obj instanceof ComponentPointer) {
                ComponentPointer ptr = (ComponentPointer) obj;
                if (ptr.interfaceId == interfaceId) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * Gets the component at the specified interface and child ID
     *
     * @param interfaceId The interface ID
     * @param childId The child component ID
     * @return The Component object, or null if not found
     */
    public static Component getComponent(int interfaceId, int childId) {
        return InterfaceList.getComponent((interfaceId << 16) | childId);
    }

    /**
     * Gets the component using a combined hash (interfaceId << 16 | childId)
     *
     * @param hash The combined hash value
     * @return The Component object, or null if not found
     */
    public static Component getComponent(int hash) {
        return InterfaceList.getComponent(hash);
    }

    // clickComponent methods have been removed as they don't work correctly

    // sendComponentAction method has been removed as it was only used by the clickComponent methods

    /**
     * Sends a special action type used for certain interfaces (like smelting)
     *
     * @param hash The combined hash value
     * @return true if the action was sent
     */
    public static boolean sendSpecialAction(int hash) {
        Protocol.outboundBuffer.p1isaac(10);
        Protocol.outboundBuffer.p4(hash);
        logger.debug("Sent special action: hash=" + hash);
        return true;
    }



    // Dialog-related methods have been moved to DialogInteraction

    /**
     * Gets the text of a component
     *
     * @param parent The parent interface ID
     * @param child The child component ID
     * @return The component text, or an empty string if not found
     */
    public static String getComponentText(int parent, int child) {
        return getComponentText(parent << 16 | child);
    }

    /**
     * Gets the text of a component using a combined hash
     *
     * @param hash The combined hash value
     * @return The component text, or an empty string if not found
     */
    public static String getComponentText(int hash) {
        Component comp = getComponent(hash);
        if (comp != null && comp.text != null) {
            return comp.text.toString();
        }
        return "";
    }
    //gets last game message in chat box
    public static String getLastGameMessage() {
        return Chat.messages[Chat.size - 1].toString();
    }

    /**
     * Gets the model ID of a component
     *
     * @param hash The combined hash value
     * @return The component model ID, or -1 if not found
     */
    public static int getComponentModelId(int hash) {
        Component comp = getComponent(hash);
        if (comp != null && comp.modelId != -1) {
            return comp.modelId;
        }
        return -1;
    }

    /**
     * Gets the sprite ID of a component
     *
     * @param hash The combined hash value
     * @return The component sprite ID, or -1 if not found
     */
    public static int getComponentSpriteId(int hash) {
        Component comp = getComponent(hash);
        if (comp != null && comp.spriteId != -1) {
            return comp.spriteId;
        }
        return -1;
    }

    /**
     * Checks if a specific interface is open in the center component
     *
     * @param id The interface ID to check
     * @return true if the interface is open in the center component, false otherwise
     */
    public static boolean isCenterComponentOpen(int id) {
        ComponentPointer resizablePointer = (ComponentPointer) InterfaceList.openInterfaces.get(CENTER_RESIZABLE);
        if (resizablePointer != null) {
            return resizablePointer.interfaceId == id;
        }
        ComponentPointer fixedPointer = (ComponentPointer) InterfaceList.openInterfaces.get(CENTER_FIXED);
        if (fixedPointer != null) {
            return fixedPointer.interfaceId == id;
        }
        return false;
    }

    /**
     * Checks if a specific interface is open in the chatbox component
     *
     * @param id The interface ID to check
     * @return true if the interface is open in the chatbox component, false otherwise
     */
    public static boolean isChatComponentOpen(int id) {
        ComponentPointer ptr = (ComponentPointer) InterfaceList.openInterfaces.get(CHATBOX_INTERFACE);
        if (ptr != null) {
            return ptr.interfaceId == id;
        }
        return false;
    }

    /**
     * Gets the ID of the interface currently open in the chatbox
     *
     * @return The chatbox interface ID, or -1 if none is open
     */
    public static int getChatboxInterfaceId() {
        ComponentPointer ptr = (ComponentPointer) InterfaceList.openInterfaces.get(CHATBOX_INTERFACE);
        if (ptr != null) {
            return ptr.interfaceId;
        }
        return -1;
    }

    /**
     * Sends an action 1 packet for a component
     *
     * @param ifhash The combined hash value
     * @param slot The slot index (usually 0 for non-inventory components)
     */
    public static void ifAction1(int ifhash, int slot) {
        Protocol.outboundBuffer.p1isaac(155);
        Protocol.outboundBuffer.p4(ifhash);
        Protocol.outboundBuffer.p2(slot);
    }

    /**
     * Sends an action packet for a component
     *
     * @param actionIndex The action index (1-10)
     * @param parent The parent interface ID
     * @param child The child component ID
     * @param slot The slot index (usually 0 for non-inventory components)
     */
    public static void ifAction(int actionIndex, int parent, int child, int slot) {
        ifAction(actionIndex, parent << 16 | child, slot);
    }

    /**
     * Sends a special action packet (type 10) for a component
     *
     * @param hash The combined hash value
     */
    public static void unknownActionType(int hash) {
        Protocol.outboundBuffer.p1isaac(10);
        Protocol.outboundBuffer.p4(hash);
    }

    /**
     * Sends an action packet for a component using a combined hash
     *
     * @param actionIndex The action index (1-10)
     * @param ifhash The combined hash value
     * @param slot The slot index (usually 0 for non-inventory components)
     */
    public static void ifAction(int actionIndex, int ifhash, int slot) {
        switch(actionIndex) {
            case 1:
                Protocol.outboundBuffer.p1isaac(155);
                break;
            case 2:
                Protocol.outboundBuffer.p1isaac(196);
                break;
            case 3:
                Protocol.outboundBuffer.p1isaac(124);
                break;
            case 4:
                Protocol.outboundBuffer.p1isaac(199);
                break;
            case 5:
                Protocol.outboundBuffer.p1isaac(234);
                break;
            case 6:
                Protocol.outboundBuffer.p1isaac(168);
                break;
            case 7:
                Protocol.outboundBuffer.p1isaac(166);
                break;
            case 8:
                Protocol.outboundBuffer.p1isaac(64);
                break;
            case 9:
                Protocol.outboundBuffer.p1isaac(53);
                break;
            case 10:
                Protocol.outboundBuffer.p1isaac(9);
                break;
        }

        Protocol.outboundBuffer.p4(ifhash);
        Protocol.outboundBuffer.p2(slot);
    }

    /**
     * Checks if special attack is currently enabled
     * 
     * @return true if special attack is enabled, false otherwise
     */
    public static boolean isSpecialAttackEnabled() {
        try {
            Component specialComponent = InterfaceList.getComponent(Components.SPECIAL_ATTACK_BUTTON);
            if (specialComponent != null) {
                // Special attack is enabled when spriteId is 1050 (active), disabled when 1049 (inactive)
                return specialComponent.spriteId == 1050;
            }
        } catch (Exception e) {
            // Handle exception silently
        }
        return false;
    }

    /**
     * Sends a continue option packet for a component
     *
     * @param parent The parent interface ID
     * @param child The child component ID
     * @param opt The option index
     */
    public static void continueOpt(int parent, int child, int opt) {
        continueOpt(parent << 16 | child, opt);
    }

    /**
     * Sends a continue option packet for a component using a combined hash
     *
     * @param hash The combined hash value
     * @param opt The option index
     */
    public static void continueOpt(int hash, int opt) {
        Protocol.outboundBuffer.p1isaac(132);
        Protocol.outboundBuffer.imp4(hash);
        Protocol.outboundBuffer.ip2(opt);
    }

    /**
     * Uses a widget on an inventory item
     *
     * @param hash The combined hash value of the widget
     * @param inventoryItem The inventory item to use the widget on
     */
    public static void useWidgetOnItem(int hash, Item inventoryItem) {
        if (inventoryItem != null) {
            Protocol.outboundBuffer.p1isaac(253);
            Protocol.outboundBuffer.ip4(hash);
            Protocol.outboundBuffer.ip2add(inventoryItem.getSlot());
            Protocol.outboundBuffer.ip4(0); // Ignored by server
            Protocol.outboundBuffer.p2add(inventoryItem.getId());
            Protocol.outboundBuffer.ip2(0); // Ignored by server
        }
    }

    /**
     * Uses a widget on an NPC
     *
     * @param hash The combined hash value of the widget
     * @param npc The NPC to use the widget on
     */
    public static void useWidgetOnNpc(int hash, Npc npc) {
        if (npc != null) {
            rt4.PathFinder.findPath(rt4.PlayerList.self.movementQueueZ[0], 0, 1, false, 0, npc.getX(), 1, 0, 2, npc.getY(), rt4.PlayerList.self.movementQueueX[0]);
            rt4.Cross.x = rt4.Mouse.clickX;
            rt4.Cross.type = 2;
            rt4.Cross.milliseconds = 0;
            rt4.Cross.y = rt4.Mouse.clickY;
            Protocol.outboundBuffer.p1isaac(239);
            Protocol.outboundBuffer.ip4(hash);
            Protocol.outboundBuffer.p2add(0); // Ignored by server
            Protocol.outboundBuffer.ip2add(npc.getIndex());
        }
    }
}
