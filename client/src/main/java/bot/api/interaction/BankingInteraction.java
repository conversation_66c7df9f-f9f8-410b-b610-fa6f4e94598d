package bot.api.interaction;

import bot.util.game.entity.Item;
import rt4.Inv;
import rt4.Protocol;

import java.awt.event.KeyEvent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Provides methods for interacting with the bank interface
 * This class handles bank-related operations such as depositing and withdrawing items,
 * checking bank contents, and managing bank slots. It communicates directly with the
 * game's protocol to perform banking actions.
 */
public class BankingInteraction {

    /**
     * Gets the item ID for a specific bank slot
     *
     * @param slot The bank slot to check (0-based index)
     * @return The item ID in the specified slot, or -1 if the slot is empty or invalid
     */
    public int getItemIdForSlot(int slot) {
        Item inventoryItemInSlot = getItem(item -> item.getSlot() == slot);
        if (inventoryItemInSlot != null) {
            return inventoryItemInSlot.getId();
        }
        return -1;
    }

    /**
     * Gets the bank slot for a specific item ID
     * Returns the first slot containing the specified item ID.
     *
     * @param itemId The item ID to find in the bank
     * @return The slot containing the item, or -1 if the item is not found
     */
    public int getSlotForItemId(int itemId) {
        Item inventoryItemInSlot = getItem(item -> item.getId() == itemId);
        if (inventoryItemInSlot != null) {
            return inventoryItemInSlot.getSlot();
        }
        return -1;
    }

    /**
     * Gets the stack size (quantity) of items in a specific bank slot
     *
     * @param slot The bank slot to check (0-based index)
     * @return The quantity of items in the slot, or 0 if the slot is empty or invalid
     */
    public int getSlotStackSize(int slot) {
        Item inventoryItemInSlot = getItem(item -> item.getSlot() == slot);
        if (inventoryItemInSlot != null) {
            return inventoryItemInSlot.getQuantity();
        }
        return 0;
    }

    /**
     * Gets the total count of a specific item in the bank
     * This method sums up the quantities of all stacks of the specified item
     * across all bank slots. This is useful for items that may be stored in
     * multiple slots or have stack limits.
     *
     * @param itemId The item ID to count in the bank
     * @return The total quantity of the specified item in the bank
     */
    public int getCount(int itemId) {
        int count = 0;
        for (Item i : getAll()) {
            if (i.getId() == itemId) {
                count += i.getQuantity();
            }
        }
        return count;
    }



    /**
     * Checks if the bank contains any of the specified item IDs
     * This method is useful for checking if the bank contains any of several
     * alternative items, such as different types of logs or ores.
     *
     * @param ids The item IDs to check for in the bank
     * @return true if the bank contains at least one of the specified items, false otherwise
     */
    public boolean contains(int... ids) {
        return getAll().stream().anyMatch(item -> Arrays.stream(ids).anyMatch(i -> i == item.getId()));
    }






    /**
     * Checks if the bank interface is currently open
     * This method verifies if the bank widget is visible on the screen.
     *
     * @return true if the bank interface is open, false otherwise
     */
    public static boolean isOpen() {
        return WidgetInteraction.isCenterComponentOpen(WidgetInteraction.Components.BANK);
    }

    /**
     * Closes the bank interface
     * This method sends a protocol packet to close the bank interface.
     * It works regardless of which bank interface is currently open.
     */
    public static void close() {
        //Send close interface packet, no extra params
        Protocol.outboundBuffer.p1isaac(184);
    }

    /**
     * Withdraws items from the bank using a specific action index
     * This is the core method used by all the specific withdraw methods (withdraw1, withdraw5, etc.).
     * It finds the item in the bank and performs the specified action on it.
     *
     * @param index The action index to perform (1=Withdraw-1, 2=Withdraw-5, etc.)
     * @param itemId The ID of the item to withdraw
     */
    public void withdraw(int index, int itemId) {
        Item inventoryItem = getItem(itemId);
        if (inventoryItem != null && isOpen()) {
            WidgetInteraction.ifAction(index, WidgetInteraction.Components.BANK_WITHDRAW, inventoryItem.getSlot());
        }
    }

    /**
     * Withdraws 1 of the specified item from the bank
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdraw1(int itemId) {
        withdraw(1, itemId);
    }



    /**
     * Withdraws 5 of the specified item from the bank
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdraw5(int itemId) {
        withdraw(2, itemId);
    }


    /**
     * Withdraws 10 of the specified item from the bank
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdraw10(int itemId) {
        withdraw(3, itemId);
    }



    /**
     * Opens the "Withdraw-X" input dialog for the specified item
     * This allows entering a custom amount to withdraw.
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdrawXAmount(int itemId) {
        withdraw(4, itemId);
    }

    /**
     * Withdraws a custom amount of the specified item
     * This opens the "Withdraw-X" dialog, enters the specified amount, and confirms.
     *
     * @param itemId The ID of the item to withdraw
     * @param amount The amount to withdraw
     */
    public void withdrawCustomAmount(int itemId, int amount) {
        if (!isOpen()) {
            return;
        }

        // First open the Withdraw-X dialog
        withdrawXAmount(itemId);

        // Wait for the dialog to open
        try {
            Thread.sleep(500);

            // Type each digit of the amount
            String amountStr = String.valueOf(amount);
            for (char c : amountStr.toCharArray()) {
                // Convert char to keycode and send key event
                int keyCode = (int) c;
                KeyEvent keyPress = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_PRESSED, System.currentTimeMillis(), 0, keyCode, c);
                rt4.client.canvas.dispatchEvent(keyPress);

                KeyEvent keyTyped = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_TYPED, System.currentTimeMillis(), 0, 0, c);
                rt4.client.canvas.dispatchEvent(keyTyped);

                KeyEvent keyRelease = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_RELEASED, System.currentTimeMillis(), 0, keyCode, c);
                rt4.client.canvas.dispatchEvent(keyRelease);

                Thread.sleep(50); // Small delay between keypresses
            }

            // Press Enter to confirm
            KeyEvent enterPress = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_PRESSED, System.currentTimeMillis(), 0, KeyEvent.VK_ENTER, '\n');
            rt4.client.canvas.dispatchEvent(enterPress);

            KeyEvent enterTyped = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_TYPED, System.currentTimeMillis(), 0, 0, '\n');
            rt4.client.canvas.dispatchEvent(enterTyped);

            KeyEvent enterRelease = new KeyEvent(rt4.client.canvas, KeyEvent.KEY_RELEASED, System.currentTimeMillis(), 0, KeyEvent.VK_ENTER, '\n');
            rt4.client.canvas.dispatchEvent(enterRelease);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }



    /**
     * Withdraws the previously set X amount of the specified item
     * This uses the last amount entered in the "Withdraw-X" dialog.
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdrawX(int itemId) {
        withdraw(5, itemId);
    }



    /**
     * Withdraws all of the specified item from the bank
     * This withdraws the entire stack of the item from the bank.
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdrawAll(int itemId) {
        withdraw(6, itemId);
    }



    /**
     * Withdraws all but one of the specified item from the bank
     * This is useful for items that are needed for both the inventory and bank,
     * such as teleport jewelry or tools.
     *
     * @param itemId The ID of the item to withdraw
     */
    public void withdrawAllButOne(int itemId) {
        withdraw(7, itemId);
    }



    /**
     * Deposits items into the bank using a specific action index
     * This is the core method used by all the specific deposit methods (deposit1, deposit5, etc.).
     * It finds the item in the inventory and performs the deposit action on it.
     *
     * @param index The action index to perform (1=Deposit-1, 2=Deposit-5, etc.)
     * @param itemId The ID of the item to deposit
     * @throws InterruptedException if the thread is interrupted during the operation
     */
    public static void deposit(int index, int itemId) throws InterruptedException {
        InventoryInteraction.interactWithInventoryItem(itemId, "Deposit-All");
    }

    /**
     * Deposits 1 of the specified item into the bank
     *
     * @param itemId The ID of the item to deposit
     * @throws InterruptedException if the thread is interrupted during the operation
     */
    public void deposit1(int itemId) throws InterruptedException {
        deposit(1, itemId);
    }



    /**
     * Deposits 5 of the specified item into the bank
     *
     * @param itemId The ID of the item to deposit
     * @throws InterruptedException if the thread is interrupted during the operation
     */
    public void deposit5(int itemId) throws InterruptedException {
        deposit(2, itemId);
    }



    /**
     * Deposits 10 of the specified item into the bank
     *
     * @param itemId The ID of the item to deposit
     * @throws InterruptedException if the thread is interrupted during the operation
     */
    public void deposit10(int itemId) throws InterruptedException {
        deposit(3, itemId);
    }



    public void depositXAmount(int itemId) throws InterruptedException {
        deposit(4, itemId);
    }



    public void depositX(int itemId) throws InterruptedException {
        deposit(5, itemId);
    }



    public static void depositAll(int itemId) throws InterruptedException {
        deposit(6, itemId);
    }

    /**
     * Deposits all items in the inventory into the bank
     * This method iterates through all inventory items and deposits each one
     *
     * @throws InterruptedException if the thread is interrupted during the operation
     */
    public static void depositAllItems() throws InterruptedException {
        if (isOpen()) {
            // Get the inventory component
            rt4.Component inventory = InventoryInteraction.getInventoryComponent();
            if (inventory == null) {
                return;
            }

            // Keep track of already deposited item IDs to avoid duplicates
            java.util.Set<Integer> depositedIds = new java.util.HashSet<>();

            // Iterate through all inventory slots
            for (int i = 0; i < inventory.objTypes.length; i++) {
                int itemId = inventory.objTypes[i];

                // Skip empty slots and already deposited item IDs
                if (itemId <= 0 || depositedIds.contains(itemId)) {
                    continue;
                }

                // Deposit all of this item
                depositAll(itemId);
                depositedIds.add(itemId);

                // Add a small delay between deposits
                Thread.sleep(300);
            }
        }
    }



    public Item getItem(int id) {
        return getItem(item -> item.getId() == id);
    }

    public Item getItem(int... ids) {
        return getItem(item -> Arrays.stream(ids).anyMatch(i -> i == item.getId()));
    }

    public Item getItem(Predicate<Item> filter) {
        return getAll().stream().filter(filter).findFirst().orElse(null);
    }

    public List<Item> getAll(Predicate<Item> filter) {
        return getAll().stream().filter(filter).collect(Collectors.toList());
    }

    public List<Item> getAll() {
        List<Item> inventoryItems = new ArrayList<>();
        int BANK_CONTAINER_ID = 95;
        Inv inv = (Inv) Inv.objectContainerCache.get(BANK_CONTAINER_ID);
        if (inv != null) {
            for (int i = 0; i < inv.objectIds.length; i++) {
                if (inv.objectIds[i] != -1) {
                    // Add 1 to the item ID to correct the offset between bank container IDs and actual item IDs
                    inventoryItems.add(new Item(inv.objectIds[i] + 1, inv.objectStackSizes[i], i));
                }
            }
        }
        return inventoryItems;
    }
}
