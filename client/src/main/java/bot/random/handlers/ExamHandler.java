package bot.random.handlers;

import bot.api.interaction.DialogInteraction;
import bot.api.interaction.NPCInteraction;
import bot.random.RandomEventHandler;
import bot.util.game.entity.Npc;
import bot.util.game.entity.Player;
import bot.util.game.world.Tile;
import rt4.InterfaceList;
import rt4.Component;
import bot.impl.input.MouseHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

/**
 * Handler for the Exam random event
 */
public class ExamHandler extends RandomEventHandler {
    private static final int EXAM_NPC_ID = 3648;
    private static final int EXAM_INTERFACE_ID = 103;
    private static final int EXAM_QUESTION_INTERFACE_ID = 104;
    private static final int EXAM_ANSWER_INTERFACE_ID = 105;

    private Npc examiner = null;
    private DialogInteraction dialogInteraction = null;

    // Exam state
    private String currentQuestion = null;
    private Map<String, String> answerMap = new HashMap<>();

    @Override
    public boolean shouldActivate() {
        // Check if we're in the Exam Center area
        Tile playerLocation = Player.getLocation();
        if (playerLocation == null) {
            return false;
        }

        // The Exam random is in a specific area
        if (playerLocation.getX() >= 3055 && playerLocation.getX() <= 3065 &&
            playerLocation.getY() >= 4975 && playerLocation.getY() <= 4985) {
            return true;
        }

        // Check if the exam interface is open
        return isExamInterfaceOpen();
    }

    @Override
    public void onStart() {
        super.onStart();

        // Initialize dialog interaction
        dialogInteraction = new DialogInteraction(
            java.util.List.of("I'll help", "Yes", "Sure", "Okay"),
            java.util.List.of(),
            true
        );

        // Initialize answer map with known question-answer pairs
        initializeAnswerMap();
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Handle dialog first
        if (dialogInteraction.handleDialog()) {
            setStatus("Handling dialog");
            return true;
        }

        // Check if the exam interface is open
        if (isExamInterfaceOpen()) {
            setStatus("Taking exam");
            return takeExam();
        }

        // Find the examiner NPC
        if (examiner == null || !examiner.exists()) {
            examiner = findExaminer();
        }

        // If we can't find the examiner, we're done
        if (examiner == null) {
            setStatus("Examiner not found, stopping");
            return false;
        }

        // Talk to the examiner
        setStatus("Talking to examiner");
        try {
            NPCInteraction.interactWithNPC(examiner, "Talk-to");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error interacting with examiner", e);
        }

        // Wait a bit for the dialog to appear
        sleep(1000, 1500);

        return true;
    }

    /**
     * Initializes the answer map with known question-answer pairs
     */
    private void initializeAnswerMap() {
        // Add known question-answer pairs
        answerMap.put("What is 2 + 2?", "4");
        answerMap.put("What is 7 + 3?", "10");
        answerMap.put("What is 10 - 4?", "6");
        answerMap.put("What is 10 + 10?", "20");
        answerMap.put("What is 2 x 4?", "8");
        answerMap.put("What is 3 x 3?", "9");
        answerMap.put("What is 4 x 4?", "16");
        answerMap.put("What is 5 x 5?", "25");
        answerMap.put("What is 6 x 6?", "36");
        answerMap.put("What is 7 x 7?", "49");
        answerMap.put("What is 8 x 8?", "64");
        answerMap.put("What is 9 x 9?", "81");
        answerMap.put("What is 10 x 10?", "100");
        answerMap.put("What is 12 x 12?", "144");

        // Add more question-answer pairs as needed
        answerMap.put("How many legs does a spider have?", "8");
        answerMap.put("How many legs does a chicken have?", "2");
        answerMap.put("How many sides does a triangle have?", "3");
        answerMap.put("How many sides does a square have?", "4");
        answerMap.put("How many sides does a pentagon have?", "5");
        answerMap.put("How many sides does a hexagon have?", "6");
        answerMap.put("How many sides does an octagon have?", "8");

        // RuneScape specific questions
        answerMap.put("What color is the default color of a fire cape?", "Orange");
        answerMap.put("What color is the default color of a magic cape?", "Blue");
        answerMap.put("What color is the default color of a ranging cape?", "Green");
        answerMap.put("What color is the default color of a strength cape?", "Red");
        answerMap.put("What color is the default color of an attack cape?", "Yellow");
        answerMap.put("What color is the default color of a hitpoints cape?", "Red");
        answerMap.put("What color is the default color of a defence cape?", "Blue");
        answerMap.put("What color is the default color of a prayer cape?", "White");

        // RuneScape capital questions
        answerMap.put("What is the capital of Misthalin?", "Varrock");
        answerMap.put("What is the capital of Asgarnia?", "Falador");
        answerMap.put("What is the capital of Kandarin?", "Ardougne");
        answerMap.put("What is the capital of Morytania?", "Canifis");
        answerMap.put("What is the capital of the Kharidian Desert?", "Al Kharid");
        answerMap.put("What is the capital of the Fremennik Province?", "Rellekka");
        answerMap.put("What is the capital of the Wilderness?", "Edgeville");
        answerMap.put("What is the capital of Tirannwn?", "Prifddinas");
        answerMap.put("What is the capital of the Feldip Hills?", "Oo'glog");
        answerMap.put("What is the capital of the Troll Country?", "Trollheim");
        answerMap.put("What is the capital of the Gnome Empire?", "Tree Gnome Stronghold");
        answerMap.put("What is the capital of the Dwarven Realm?", "Keldagrim");

        // Add more RuneScape specific questions as needed
    }

    /**
     * Finds the Examiner NPC
     *
     * @return The Examiner NPC, or null if not found
     */
    private Npc findExaminer() {
        try {
            return NPCInteraction.findNearestNPC(npc ->
                npc != null &&
                npc.getName() != null &&
                npc.getName().equals("Mr. Mordaut")
            );
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error finding Examiner", e);
            return null;
        }
    }

    /**
     * Checks if the exam interface is open
     *
     * @return true if the exam interface is open
     */
    private boolean isExamInterfaceOpen() {
        return InterfaceList.getComponent(EXAM_INTERFACE_ID << 16) != null;
    }

    /**
     * Takes the exam
     *
     * @return true if the exam is still being taken, false if it's complete
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean takeExam() throws InterruptedException {
        // Get the current question
        String question = getCurrentQuestion();
        if (question == null || question.isEmpty()) {
            return false;
        }

        // Get the answer for the current question
        String answer = getAnswerForQuestion(question);
        if (answer == null || answer.isEmpty()) {
            // If we don't know the answer, log the error and close the interface
            // It's better to exit the random event than to guess and potentially fail
            logger.log(Level.WARNING, "Unknown exam question: " + question);
            Component closeButton = findCloseButton();
            if (closeButton != null) {
                // Use MouseInteractionUtil to click the close button
                MouseHandler.clickComponent(closeButton);

                // Wait a bit for the click to register
                sleep(500, 800);
            }
            return false;
        }

        // Find the answer option that matches our answer
        int answerIndex = findAnswerOptionIndex(answer);
        if (answerIndex != -1) {
            clickAnswerOption(answerIndex);
            return true;
        }

        // If we can't find the answer option, log the error and close the interface
        logger.log(Level.WARNING, "Could not find answer option for: " + answer);
        Component closeButton = findCloseButton();
        if (closeButton != null) {
            // Use MouseInteractionUtil to click the close button
            MouseHandler.clickComponent(closeButton);

            // Wait a bit for the click to register
            sleep(500, 800);
        }
        return false;
    }

    /**
     * Gets the current exam question
     *
     * @return The current question, or null if not found
     */
    private String getCurrentQuestion() {
        Component questionInterface = InterfaceList.getComponent(EXAM_QUESTION_INTERFACE_ID << 16);
        if (questionInterface != null && questionInterface.text != null) {
            return questionInterface.text.toString();
        }

        return null;
    }

    /**
     * Gets the answer for the given question
     *
     * @param question The question to get the answer for
     * @return The answer, or null if not found
     */
    private String getAnswerForQuestion(String question) {
        return answerMap.get(question);
    }

    /**
     * Finds the index of the answer option that matches the given answer
     *
     * @param answer The answer to find
     * @return The index of the answer option, or -1 if not found
     */
    private int findAnswerOptionIndex(String answer) {
        Component answerInterface = InterfaceList.getComponent(EXAM_ANSWER_INTERFACE_ID << 16);
        if (answerInterface == null) {
            return -1;
        }

        // The answer options are child components of the answer interface
        // We need to iterate through the child components by index
        for (int i = 0; i < 10; i++) { // Assuming a reasonable maximum number of options
            Component option = InterfaceList.getComponent((EXAM_ANSWER_INTERFACE_ID << 16) | i);
            if (option != null && option.text != null && option.text.toString().equals(answer)) {
                return i;
            }
        }

        return -1;
    }

    /**
     * Clicks the answer option at the given index
     *
     * @param index The index of the answer option to click
     * @throws InterruptedException if the thread is interrupted
     */
    private void clickAnswerOption(int index) throws InterruptedException {
        // Get the child component directly using the composite ID
        Component option = InterfaceList.getComponent((EXAM_ANSWER_INTERFACE_ID << 16) | index);
        if (option != null) {
            // Use MouseInteractionUtil to click the option
            MouseHandler.clickComponent(option);

            // Wait a bit for the click to register
            sleep(500, 800);
        }
    }

    /**
     * Finds the close button in the exam interface
     *
     * @return The close button component, or null if not found
     */
    private Component findCloseButton() {
        // The close button is usually a child of the exam interface
        // It typically has a specific sprite ID or text like "Close"
        for (int i = 0; i < 50; i++) { // Assuming a reasonable maximum number of children
            Component child = InterfaceList.getComponent((EXAM_INTERFACE_ID << 16) | i);
            if (child != null) {
                // Check if it's a close button by sprite ID or text
                if ((child.spriteId == 535 || child.spriteId == 537) || // Common close button sprite IDs
                    (child.text != null && child.text.toString().contains("Close"))) {
                    return child;
                }
            }
        }
        return null;
    }


}
